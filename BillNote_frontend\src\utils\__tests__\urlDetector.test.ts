import { detectVideoPlatform, detectVideoUrlFromText } from '../urlDetector'

describe('URL检测功能测试', () => {
  describe('detectVideoPlatform', () => {
    test('应该正确识别Bilibili标准链接', () => {
      const result = detectVideoPlatform('https://www.bilibili.com/video/BV1vc411b7Wa')
      expect(result.platform).toBe('bilibili')
      expect(result.isValid).toBe(true)
      expect(result.videoId).toBe('BV1vc411b7Wa')
      expect(result.confidence).toBeGreaterThan(0.9)
    })

    test('应该正确识别Bilibili短链接', () => {
      const result = detectVideoPlatform('https://b23.tv/abc123')
      expect(result.platform).toBe('bilibili')
      expect(result.isValid).toBe(true)
      expect(result.confidence).toBeGreaterThan(0.8)
    })

    test('应该正确识别YouTube标准链接', () => {
      const result = detectVideoPlatform('https://www.youtube.com/watch?v=dQw4w9WgXcQ')
      expect(result.platform).toBe('youtube')
      expect(result.isValid).toBe(true)
      expect(result.videoId).toBe('dQw4w9WgXcQ')
      expect(result.confidence).toBeGreaterThan(0.9)
    })

    test('应该正确识别YouTube短链接', () => {
      const result = detectVideoPlatform('https://youtu.be/dQw4w9WgXcQ')
      expect(result.platform).toBe('youtube')
      expect(result.isValid).toBe(true)
      expect(result.videoId).toBe('dQw4w9WgXcQ')
      expect(result.confidence).toBeGreaterThan(0.9)
    })

    test('应该正确识别YouTube Shorts链接', () => {
      const result = detectVideoPlatform('https://www.youtube.com/shorts/dQw4w9WgXcQ')
      expect(result.platform).toBe('youtube')
      expect(result.isValid).toBe(true)
      expect(result.videoId).toBe('dQw4w9WgXcQ')
      expect(result.confidence).toBeGreaterThan(0.9)
    })

    test('应该正确识别抖音链接', () => {
      const result = detectVideoPlatform('https://www.douyin.com/video/1234567890123456789')
      expect(result.platform).toBe('douyin')
      expect(result.isValid).toBe(true)
      expect(result.videoId).toBe('1234567890123456789')
      expect(result.confidence).toBeGreaterThan(0.9)
    })

    test('应该处理无效链接', () => {
      const result = detectVideoPlatform('invalid-url')
      expect(result.platform).toBe(null)
      expect(result.isValid).toBe(false)
      expect(result.confidence).toBe(0)
    })

    test('应该处理空字符串', () => {
      const result = detectVideoPlatform('')
      expect(result.platform).toBe(null)
      expect(result.isValid).toBe(false)
      expect(result.confidence).toBe(0)
    })

    test('应该识别域名但标记为无效格式', () => {
      const result = detectVideoPlatform('https://www.bilibili.com/invalid-path')
      expect(result.platform).toBe('bilibili')
      expect(result.isValid).toBe(false)
      expect(result.confidence).toBe(0.3)
      expect(result.detectedPattern).toBe('domain_only')
    })
  })

  describe('detectVideoUrlFromText', () => {
    test('应该从文本中提取视频链接', () => {
      const text = '看看这个视频：https://www.bilibili.com/video/BV1vc411b7Wa 很有趣！'
      const result = detectVideoUrlFromText(text)
      expect(result.platform).toBe('bilibili')
      expect(result.isValid).toBe(true)
      expect(result.videoId).toBe('BV1vc411b7Wa')
    })

    test('应该选择置信度最高的链接', () => {
      const text = 'https://www.bilibili.com/invalid https://www.youtube.com/watch?v=dQw4w9WgXcQ'
      const result = detectVideoUrlFromText(text)
      expect(result.platform).toBe('youtube')
      expect(result.isValid).toBe(true)
    })

    test('应该处理包含中文的文本', () => {
      const text = '这是一个很棒的视频：https://youtu.be/dQw4w9WgXcQ，推荐观看！'
      const result = detectVideoUrlFromText(text)
      expect(result.platform).toBe('youtube')
      expect(result.isValid).toBe(true)
    })

    test('应该处理没有链接的文本', () => {
      const text = '这只是普通的文本，没有任何链接'
      const result = detectVideoUrlFromText(text)
      expect(result.platform).toBe(null)
      expect(result.isValid).toBe(false)
    })
  })
})

import React from 'react'
import { CheckCircle, AlertCircle, XCircle, Loader2 } from 'lucide-react'
import { Badge } from '@/components/ui/badge.tsx'
import { videoPlatforms } from '@/constant/note.ts'
import { PlatformDetectionResult } from '@/utils/urlDetector'

interface UrlDetectionStatusProps {
  detection: PlatformDetectionResult | null
  isDetecting: boolean
  className?: string
}

export const UrlDetectionStatus: React.FC<UrlDetectionStatusProps> = ({
  detection,
  isDetecting,
  className = ''
}) => {
  if (isDetecting) {
    return (
      <div className={`flex items-center gap-2 text-sm text-gray-500 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>正在检测链接...</span>
      </div>
    )
  }

  if (!detection || !detection.platform) {
    return null
  }

  const platform = videoPlatforms.find(p => p.value === detection.platform)
  const confidence = detection.confidence || 0

  const getStatusIcon = () => {
    if (detection.isValid && confidence > 0.8) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else if (detection.isValid && confidence > 0.5) {
      return <AlertCircle className="h-4 w-4 text-yellow-500" />
    } else {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = () => {
    if (detection.isValid && confidence > 0.8) {
      return '链接格式正确'
    } else if (detection.isValid && confidence > 0.5) {
      return '链接可能有效'
    } else {
      return '链接格式可能有误'
    }
  }

  const getConfidenceColor = () => {
    if (confidence > 0.8) return 'bg-green-100 text-green-800'
    if (confidence > 0.5) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  return (
    <div className={`flex items-center gap-2 text-sm ${className}`}>
      {getStatusIcon()}
      <span className="text-gray-700">
        检测到 {platform?.label} 链接
      </span>
      <Badge variant="secondary" className={getConfidenceColor()}>
        {Math.round(confidence * 100)}%
      </Badge>
      <span className="text-gray-500 text-xs">
        {getStatusText()}
      </span>
      {detection.detectedPattern && (
        <span className="text-gray-400 text-xs">
          ({detection.detectedPattern})
        </span>
      )}
    </div>
  )
}

export default UrlDetectionStatus

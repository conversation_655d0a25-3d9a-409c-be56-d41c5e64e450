/**
 * URL平台检测工具
 * 自动识别视频链接来自哪个平台
 */

export interface PlatformDetectionResult {
  platform: string | null
  isValid: boolean
  videoId?: string
}

/**
 * 检测URL并返回对应的平台
 * @param url 视频链接
 * @returns 平台检测结果
 */
export function detectVideoPlatform(url: string): PlatformDetectionResult {
  if (!url || typeof url !== 'string') {
    return { platform: null, isValid: false }
  }

  // 清理URL，移除多余的空格
  const cleanUrl = url.trim()

  // Bilibili 检测
  const bilibiliPatterns = [
    /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/([a-zA-Z0-9]+)/,
    /(?:https?:\/\/)?(?:www\.)?b23\.tv\/([a-zA-Z0-9]+)/,
    /BV([0-9A-Za-z]+)/
  ]

  for (const pattern of bilibiliPatterns) {
    const match = cleanUrl.match(pattern)
    if (match) {
      return {
        platform: 'bilibili',
        isValid: true,
        videoId: match[1]
      }
    }
  }

  // YouTube 检测
  const youtubePatterns = [
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})/
  ]

  for (const pattern of youtubePatterns) {
    const match = cleanUrl.match(pattern)
    if (match) {
      return {
        platform: 'youtube',
        isValid: true,
        videoId: match[1]
      }
    }
  }

  // 抖音检测
  const douyinPatterns = [
    /(?:https?:\/\/)?(?:www\.)?douyin\.com\/video\/(\d+)/,
    /(?:https?:\/\/)?v\.douyin\.com\/([a-zA-Z0-9]+)/
  ]

  for (const pattern of douyinPatterns) {
    const match = cleanUrl.match(pattern)
    if (match) {
      return {
        platform: 'douyin',
        isValid: true,
        videoId: match[1]
      }
    }
  }

  // 快手检测
  const kuaishouPatterns = [
    /(?:https?:\/\/)?(?:www\.)?kuaishou\.com\/short-video\/(\d+)/,
    /(?:https?:\/\/)?v\.kuaishou\.com\/([a-zA-Z0-9]+)/
  ]

  for (const pattern of kuaishouPatterns) {
    const match = cleanUrl.match(pattern)
    if (match) {
      return {
        platform: 'kuaishou',
        isValid: true,
        videoId: match[1]
      }
    }
  }

  // 如果包含常见的视频网站域名但没有匹配到具体模式，仍然尝试识别
  if (cleanUrl.includes('bilibili.com') || cleanUrl.includes('b23.tv')) {
    return { platform: 'bilibili', isValid: false }
  }
  
  if (cleanUrl.includes('youtube.com') || cleanUrl.includes('youtu.be')) {
    return { platform: 'youtube', isValid: false }
  }
  
  if (cleanUrl.includes('douyin.com')) {
    return { platform: 'douyin', isValid: false }
  }
  
  if (cleanUrl.includes('kuaishou.com')) {
    return { platform: 'kuaishou', isValid: false }
  }

  // 检查是否是有效的URL格式
  try {
    new URL(cleanUrl.startsWith('http') ? cleanUrl : `https://${cleanUrl}`)
    return { platform: null, isValid: false }
  } catch {
    return { platform: null, isValid: false }
  }
}

/**
 * 检查剪贴板内容是否包含视频链接
 * @param text 剪贴板文本
 * @returns 检测结果
 */
export function detectVideoUrlFromText(text: string): PlatformDetectionResult {
  if (!text) {
    return { platform: null, isValid: false }
  }

  // 尝试从文本中提取URL
  const urlRegex = /(https?:\/\/[^\s]+)/g
  const urls = text.match(urlRegex)
  
  if (urls && urls.length > 0) {
    // 检测第一个找到的URL
    return detectVideoPlatform(urls[0])
  }

  // 如果没有找到完整URL，尝试检测是否包含视频ID模式
  return detectVideoPlatform(text)
}

/**
 * URL平台检测工具
 * 自动识别视频链接来自哪个平台
 */

export interface PlatformDetectionResult {
  platform: string | null
  isValid: boolean
  videoId?: string
  confidence?: number // 添加置信度评分
  detectedPattern?: string // 添加检测到的模式信息
}

/**
 * 检测URL并返回对应的平台
 * @param url 视频链接
 * @returns 平台检测结果
 */
export function detectVideoPlatform(url: string): PlatformDetectionResult {
  if (!url || typeof url !== 'string') {
    return { platform: null, isValid: false, confidence: 0 }
  }

  // 清理URL，移除多余的空格和常见的无关字符
  const cleanUrl = url.trim().replace(/[，。！？；：""''（）【】]/g, '')

  // Bilibili 检测 - 增强模式匹配
  const bilibiliPatterns = [
    { pattern: /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(BV[0-9A-Za-z]+)/, confidence: 0.95, name: 'bilibili_standard' },
    { pattern: /(?:https?:\/\/)?(?:www\.)?b23\.tv\/([a-zA-Z0-9]+)/, confidence: 0.90, name: 'bilibili_short' },
    { pattern: /(?:https?:\/\/)?(?:m\.)?bilibili\.com\/video\/(BV[0-9A-Za-z]+)/, confidence: 0.95, name: 'bilibili_mobile' },
    { pattern: /BV([0-9A-Za-z]{10})/, confidence: 0.85, name: 'bilibili_bv_only' },
    { pattern: /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(av\d+)/, confidence: 0.80, name: 'bilibili_av' }
  ]

  for (const { pattern, confidence, name } of bilibiliPatterns) {
    const match = cleanUrl.match(pattern)
    if (match) {
      return {
        platform: 'bilibili',
        isValid: true,
        videoId: match[1],
        confidence,
        detectedPattern: name
      }
    }
  }

  // YouTube 检测 - 增强模式匹配
  const youtubePatterns = [
    { pattern: /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/, confidence: 0.95, name: 'youtube_standard' },
    { pattern: /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/, confidence: 0.95, name: 'youtube_short' },
    { pattern: /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/, confidence: 0.90, name: 'youtube_embed' },
    { pattern: /(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})/, confidence: 0.85, name: 'youtube_v' },
    { pattern: /(?:https?:\/\/)?(?:m\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/, confidence: 0.95, name: 'youtube_mobile' },
    { pattern: /(?:https?:\/\/)?(?:www\.)?youtube\.com\/shorts\/([a-zA-Z0-9_-]{11})/, confidence: 0.95, name: 'youtube_shorts' }
  ]

  for (const { pattern, confidence, name } of youtubePatterns) {
    const match = cleanUrl.match(pattern)
    if (match) {
      return {
        platform: 'youtube',
        isValid: true,
        videoId: match[1],
        confidence,
        detectedPattern: name
      }
    }
  }

  // 抖音检测 - 增强模式匹配
  const douyinPatterns = [
    { pattern: /(?:https?:\/\/)?(?:www\.)?douyin\.com\/video\/(\d+)/, confidence: 0.95, name: 'douyin_standard' },
    { pattern: /(?:https?:\/\/)?v\.douyin\.com\/([a-zA-Z0-9]+)/, confidence: 0.90, name: 'douyin_short' },
    { pattern: /(?:https?:\/\/)?(?:m\.)?douyin\.com\/video\/(\d+)/, confidence: 0.95, name: 'douyin_mobile' }
  ]

  for (const { pattern, confidence, name } of douyinPatterns) {
    const match = cleanUrl.match(pattern)
    if (match) {
      return {
        platform: 'douyin',
        isValid: true,
        videoId: match[1],
        confidence,
        detectedPattern: name
      }
    }
  }

  // 快手检测 - 增强模式匹配
  const kuaishouPatterns = [
    { pattern: /(?:https?:\/\/)?(?:www\.)?kuaishou\.com\/short-video\/(\d+)/, confidence: 0.95, name: 'kuaishou_standard' },
    { pattern: /(?:https?:\/\/)?v\.kuaishou\.com\/([a-zA-Z0-9]+)/, confidence: 0.90, name: 'kuaishou_short' },
    { pattern: /(?:https?:\/\/)?(?:m\.)?kuaishou\.com\/short-video\/(\d+)/, confidence: 0.95, name: 'kuaishou_mobile' }
  ]

  for (const { pattern, confidence, name } of kuaishouPatterns) {
    const match = cleanUrl.match(pattern)
    if (match) {
      return {
        platform: 'kuaishou',
        isValid: true,
        videoId: match[1],
        confidence,
        detectedPattern: name
      }
    }
  }

  // 如果包含常见的视频网站域名但没有匹配到具体模式，仍然尝试识别
  if (cleanUrl.includes('bilibili.com') || cleanUrl.includes('b23.tv')) {
    return { platform: 'bilibili', isValid: false, confidence: 0.3, detectedPattern: 'domain_only' }
  }

  if (cleanUrl.includes('youtube.com') || cleanUrl.includes('youtu.be')) {
    return { platform: 'youtube', isValid: false, confidence: 0.3, detectedPattern: 'domain_only' }
  }

  if (cleanUrl.includes('douyin.com')) {
    return { platform: 'douyin', isValid: false, confidence: 0.3, detectedPattern: 'domain_only' }
  }

  if (cleanUrl.includes('kuaishou.com')) {
    return { platform: 'kuaishou', isValid: false, confidence: 0.3, detectedPattern: 'domain_only' }
  }

  // 检查是否是有效的URL格式
  try {
    new URL(cleanUrl.startsWith('http') ? cleanUrl : `https://${cleanUrl}`)
    return { platform: null, isValid: false, confidence: 0 }
  } catch {
    return { platform: null, isValid: false, confidence: 0 }
  }
}

/**
 * 检查剪贴板内容是否包含视频链接
 * @param text 剪贴板文本
 * @returns 检测结果
 */
export function detectVideoUrlFromText(text: string): PlatformDetectionResult {
  if (!text) {
    return { platform: null, isValid: false, confidence: 0 }
  }

  // 尝试从文本中提取URL - 增强正则表达式
  const urlRegex = /(https?:\/\/[^\s\u4e00-\u9fa5]+|(?:www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\/[^\s\u4e00-\u9fa5]*)/g
  const urls = text.match(urlRegex)

  if (urls && urls.length > 0) {
    // 检测所有找到的URL，返回置信度最高的
    let bestResult: PlatformDetectionResult = { platform: null, isValid: false, confidence: 0 }

    for (const url of urls) {
      const result = detectVideoPlatform(url)
      if (result.confidence && result.confidence > (bestResult.confidence || 0)) {
        bestResult = result
      }
    }

    if (bestResult.platform) {
      return bestResult
    }

    // 如果没有找到高置信度的结果，返回第一个URL的检测结果
    return detectVideoPlatform(urls[0])
  }

  // 如果没有找到完整URL，尝试检测是否包含视频ID模式
  return detectVideoPlatform(text)
}

/**
 * 智能剪贴板处理函数
 * @returns Promise<PlatformDetectionResult & { text: string }>
 */
export async function smartClipboardDetection(): Promise<PlatformDetectionResult & { text: string }> {
  try {
    if (!navigator.clipboard) {
      throw new Error('浏览器不支持剪贴板API')
    }

    const text = await navigator.clipboard.readText()
    const detection = detectVideoUrlFromText(text)

    return {
      ...detection,
      text: text.trim()
    }
  } catch (error) {
    console.error('剪贴板读取失败:', error)
    return {
      platform: null,
      isValid: false,
      confidence: 0,
      text: ''
    }
  }
}

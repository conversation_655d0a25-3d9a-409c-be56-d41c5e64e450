import React, { FC, useState, useEffect } from 'react'
import { SlidersHorizontal, RotateCcw, Maximize2 } from 'lucide-react'
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip.tsx'
import { Button } from '@/components/ui/button.tsx'
import { Link } from 'react-router-dom'
import { ResizablePanel, ResizablePanelGroup, ResizableHandle } from '@/components/ui/resizable'
import {ScrollArea} from "@/components/ui/scroll-area.tsx";
import logo from '@/assets/icon.svg'
interface IProps {
  NoteForm: React.ReactNode
  Preview: React.ReactNode
  History: React.ReactNode
}
const HomeLayout: FC<IProps> = ({ NoteForm, Preview, History }) => {
  const [, setShowSettings] = useState(false)
  const [panelSizes, setPanelSizes] = useState({ left: 18, middle: 16, right: 66 })
  const [isResizing, setIsResizing] = useState(false)

  // 默认布局预设
  const layoutPresets = {
    default: { left: 18, middle: 16, right: 66 },
    focus: { left: 25, middle: 12, right: 63 },
    preview: { left: 15, middle: 10, right: 75 },
    balanced: { left: 20, middle: 20, right: 60 }
  }

  // 保存布局到localStorage
  useEffect(() => {
    const savedLayout = localStorage.getItem('biliNote-layout')
    if (savedLayout) {
      try {
        const parsed = JSON.parse(savedLayout)
        setPanelSizes(parsed)
      } catch (error) {
        console.error('Failed to parse saved layout:', error)
      }
    }
  }, [])

  const saveLayout = (sizes: typeof panelSizes) => {
    setPanelSizes(sizes)
    localStorage.setItem('biliNote-layout', JSON.stringify(sizes))
  }

  const resetLayout = () => {
    saveLayout(layoutPresets.default)
  }

  const applyPreset = (preset: keyof typeof layoutPresets) => {
    saveLayout(layoutPresets[preset])
  }

  return (
    <div className="flex h-screen flex-col overflow-hidden">
      <ResizablePanelGroup
        direction="horizontal"
        className="h-full w-full"
        onLayout={(sizes) => {
          if (!isResizing) return
          const [left, middle, right] = sizes
          saveLayout({ left, middle, right })
        }}
      >
        {/* 左边表单 */}
        <ResizablePanel
          defaultSize={panelSizes.left}
          minSize={10}
          maxSize={35}
          onResize={() => setIsResizing(true)}
        >
          <aside className="flex h-full flex-col overflow-hidden border-r border-neutral-200 bg-white">
            <header className="flex h-16 items-center justify-between px-6">
              <div className="flex items-center gap-2">
                <div className="flex h-10 w-10 items-center justify-center overflow-hidden rounded-2xl">
                  <img src={logo} alt="logo" className="h-full w-full object-contain" />
                </div>
                <div className="text-2xl font-bold text-gray-800">BiliNote</div>
              </div>
              <div className="flex items-center gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={resetLayout}
                        className="h-8 w-8"
                      >
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>重置布局</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger onClick={() => setShowSettings(true)}>
                      <Link to={'/settings'}>
                        <SlidersHorizontal className="text-muted-foreground hover:text-primary cursor-pointer" />
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>
                      <span>全局配置</span>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </header>
            <ScrollArea className="flex-1 overflow-auto">
              <div className=' p-4' >{NoteForm}</div>
            </ScrollArea>
          </aside>
        </ResizablePanel>

        <ResizableHandle
          withHandle
          className="w-1 bg-neutral-100 hover:bg-neutral-200 transition-colors duration-200 relative group"
        >
          <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-neutral-300 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        </ResizableHandle>

        {/* 中间历史 */}
        <ResizablePanel
          defaultSize={panelSizes.middle}
          minSize={10}
          maxSize={30}
          onResize={() => setIsResizing(true)}
        >
          <aside className="flex h-full flex-col overflow-hidden border-r border-neutral-200 bg-white">
            <ScrollArea className="flex-1 overflow-auto">
            <div className="">{History}</div>
            </ScrollArea>
          </aside>
        </ResizablePanel>

        <ResizableHandle
          withHandle
          className="w-1 bg-neutral-100 hover:bg-neutral-200 transition-colors duration-200 relative group"
        >
          <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-neutral-300 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        </ResizableHandle>

        {/* 右边预览 */}
        <ResizablePanel
          defaultSize={panelSizes.right}
          minSize={30}
          onResize={() => setIsResizing(true)}
        >
          <main className="flex h-full flex-col overflow-hidden bg-white p-6">{Preview}</main>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}

export default HomeLayout

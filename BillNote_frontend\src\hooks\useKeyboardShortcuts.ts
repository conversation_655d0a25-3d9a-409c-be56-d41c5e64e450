import { useEffect, useCallback } from 'react'

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  callback: () => void
  description?: string
}

/**
 * 键盘快捷键钩子
 * @param shortcuts 快捷键配置数组
 * @param enabled 是否启用快捷键
 */
export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[], enabled: boolean = true) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return

    // 如果焦点在输入框中，某些快捷键可能不应该触发
    const activeElement = document.activeElement
    const isInputFocused = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.contentEditable === 'true'
    )

    for (const shortcut of shortcuts) {
      const {
        key,
        ctrlKey = false,
        altKey = false,
        shiftKey = false,
        metaKey = false,
        callback
      } = shortcut

      // 检查按键是否匹配
      const keyMatches = event.key.toLowerCase() === key.toLowerCase()
      const modifiersMatch = 
        event.ctrlKey === ctrlKey &&
        event.altKey === altKey &&
        event.shiftKey === shiftKey &&
        event.metaKey === metaKey

      if (keyMatches && modifiersMatch) {
        // 对于某些快捷键，即使在输入框中也要触发
        const shouldTriggerInInput = ctrlKey || metaKey || altKey
        
        if (!isInputFocused || shouldTriggerInInput) {
          event.preventDefault()
          event.stopPropagation()
          callback()
          break
        }
      }
    }
  }, [shortcuts, enabled])

  useEffect(() => {
    if (!enabled) return

    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown, enabled])
}

/**
 * 格式化快捷键显示文本
 * @param shortcut 快捷键配置
 * @returns 格式化的快捷键文本
 */
export function formatShortcutText(shortcut: KeyboardShortcut): string {
  const parts: string[] = []
  
  if (shortcut.ctrlKey) parts.push('Ctrl')
  if (shortcut.metaKey) parts.push('Cmd')
  if (shortcut.altKey) parts.push('Alt')
  if (shortcut.shiftKey) parts.push('Shift')
  
  parts.push(shortcut.key.toUpperCase())
  
  return parts.join(' + ')
}

/**
 * 检测操作系统，用于显示正确的修饰键
 */
export function getModifierKey(): 'Ctrl' | 'Cmd' {
  return navigator.platform.toLowerCase().includes('mac') ? 'Cmd' : 'Ctrl'
}
